{"timestamp": "2025-06-16T15:43:49.222281+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.222545+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.222597+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.224916+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.224991+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.225067+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.228804+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.228958+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.229009+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.232306+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.232379+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.232420+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.235646+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.235820+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.235867+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.237197+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.237254+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.237293+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.252012+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.252164+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.252212+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.299428+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.299554+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.299603+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.341336+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.341476+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.341523+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.405103+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.0015}}
{"timestamp": "2025-06-16T15:43:49.405290+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.405548+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.005}}
{"timestamp": "2025-06-16T15:43:49.406652+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.406722+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.406774+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:46:48.901281+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:47:07.004859+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-16T15:47:07.007132+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-16T15:47:07.007190+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-16T15:47:20.177613+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-16T15:47:20.177915+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-16T15:47:20.178250+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-16T15:47:35.947010+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.3511642857142857}}
{"timestamp": "2025-06-16T15:47:35.948071+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4016}}
{"timestamp": "2025-06-16T15:47:35.948142+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.23348095238095237}}
{"timestamp": "2025-06-16T15:47:39.631864+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:47:46.017669+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:47:55.318211+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:48:03.186795+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:48:36.475851+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.3511642857142857}}
{"timestamp": "2025-06-16T15:48:36.476023+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4016}}
{"timestamp": "2025-06-16T15:48:36.476083+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.23348095238095237}}
{"timestamp": "2025-06-16T15:48:50.912440+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:49:11.280675+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T16:02:29.642071+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-16T16:02:29.759981+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-16T16:02:29.760155+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
