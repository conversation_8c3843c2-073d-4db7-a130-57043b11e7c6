{"start_greeting": "Hello! 👋 Welcome to EduGuideBot", "start_description": "🎓 I will help you find the most suitable study programs in Cambodia", "start_button": "Start Assessment", "assessment_title": "🎓 **EduGuideBot Skills Assessment**", "assessment_progress": "📊 Assessment: {current}/{total}", "assessment_complete": "✅ **Assessment Complete!**", "recommendations_title": "🎯 **Study Program Recommendations for You:**", "recommendation_card_details": "🔍 More Details /details_{id}", "restart_message": "🔄 Want to take the assessment again? /start", "error_recommendation": "❌ Error generating recommendations: {error}", "details_coming_soon": "🔍 Detailed information will be available in the next version. Thank you!", "cancel_message": "❌ Assessment cancelled. Want to start again? /start", "generic_error": "Sorry – something went wrong. Please try again.", "command_error": "Sorry – there was an error processing the {command} command.", "filter_language_usage": "❌ Please specify language: /filterlanguage kh, /filterlanguage en, or /filterlanguage both", "filter_language_invalid": "❌ Invalid language. Please use: kh, en, or both", "help_title": "Help & Instructions", "help_description": "EduGuideBot helps you find the perfect university program in Cambodia", "help_commands": "**Command List:**\n/start - Start assessment\n/help - Show help\n/settings - Language settings\n/cancel - Cancel assessment", "help_features": "**Features:**\n• 16-question assessment\n• Hybrid scoring system (70% MCDA + 30% ML)\n• 47 universities information\n• 500+ study programs", "help_contact": "**Contact:** For additional help, please contact the development team", "hybrid_score_explanation": "📊 **Hybrid Score = 70% MCDA + 30% ML**", "mcda_explanation": "MCDA (Multi-Criteria Decision Analysis) evaluates programs based on your preferences", "ml_explanation": "ML (Machine Learning) component learns from successful student outcomes", "score_breakdown": "📈 Score Breakdown:\n• MCDA: {mcda_score:.2f}\n• ML: {ml_score:.2f}\n• Hybrid: {hybrid_score:.2f}", "why_recommended": "🤔 **Why this recommendation?**\n{reason}", "button_back_to_list": "🔙 Back to list", "button_why_recommended": "📊 Why this rec?", "button_filters": "🔧 Filters", "button_low_cost_only": "💰 Low-cost only", "button_phnom_penh_only": "🏛️ Phnom Penh only", "settings_filters": "🔧 **Filters**", "filter_low_cost": "💰 Show only low-cost programs (<$500)", "filter_phnom_penh": "🏛️ Show only Phnom Penh programs", "filter_enabled": "✅ Enabled", "filter_disabled": "❌ Disabled", "filters_updated": "✅ Filters updated successfully", "settings_title": "⚙️ **Settings**", "settings_language": "🌐 **Language:** {language}", "settings_change_language": "Change Language", "language_khmer": "🇰🇭 Khmer", "language_english": "🇬🇧 English", "language_changed": "✅ Language changed to {language}", "question_location": "🏛️ Which city/province would you like to study in?", "question_budget": "💰 What is your annual study budget?", "question_field": "🔬 Which field interests you?", "question_career": "🎯 Future career goals?", "question_academic": "📚 Which subject are you best at?", "question_learning": "📝 What learning style do you prefer?", "question_study_mode": "⏰ What study schedule suits you?", "question_language": "🗣️ Language of instruction?", "question_scholarship": "🎓 Do you need a scholarship?", "question_campus": "🏫 How important is campus life?", "question_extracurricular": "🎨 Which extracurricular activities interest you?", "question_employment": "💼 What's most important for your career?", "question_mental_health": "🧘‍♂️ Is mental health support important to you?", "question_international": "🌏 Do you want international exposure in your program?", "question_study_plan": "📅 Would you like a personalized study plan?", "question_campus_map": "🗺️ Would you like to see university maps before deciding?", "option_pp": "Phnom Penh", "option_sr": "<PERSON><PERSON>", "option_btb": "Battambang", "option_any": "Any location", "option_low": "Low", "option_mid": "$500-$1500", "option_high": "High", "option_flex": "Flexible", "option_stem": "Science & Technology", "option_business": "Business", "option_health": "Health", "option_arts": "Arts", "option_social": "Social Sciences", "option_education": "Education", "option_tech": "Technology", "option_finance": "Finance", "option_gov": "Government", "option_entre": "Entrepreneurship", "option_unsure": "Not sure", "option_math": "Mathematics", "option_language": "Languages", "option_hands_on": "Practical", "option_all": "All subjects", "option_theory": "Theory", "option_practical": "Practical", "option_group": "Group", "option_self": "Self-study", "option_mixed": "Mixed", "option_full": "Full-time", "option_part": "Part-time", "option_evening": "Evening", "option_weekend": "Weekend", "option_kh": "Khmer", "option_en": "English", "option_both": "Both", "option_yes": "Yes", "option_partial": "Partial", "option_no": "No", "option_very": "Very important", "option_some": "Somewhat", "option_little": "A little", "option_none": "Not important", "option_sports": "Sports", "option_volunteer": "Volunteer", "option_clubs": "Clubs", "option_salary": "Salary", "option_stability": "Stability", "option_passion": "Passion", "option_growth": "Growth", "option_balance": "Work-life balance", "option_medium": "Medium", "option_maybe": "Maybe", "more_info": "More Info", "refresh": "Refresh", "restart": "<PERSON><PERSON>", "university": "University", "city": "City", "tuition": "Tuition", "employment_rate": "Employment Rate", "default_reason": "Assessment-based evaluation", "contact": "Contact", "map": "Map", "back_to_list": "Back to List", "no_recommendations": "No recommendations found", "more": "More", "compare": "Compare", "filters": "Filters", "prev": "Prev", "next": "Next", "save": "Save", "remove": "Remove", "comparison": "Comparison", "metric": "Metric", "score": "Score", "select_filter": "Select Filter", "budget_filter": "Budget Filter", "field_filter": "Field Filter", "city_filter": "City Filter", "back": "Back", "saved": "Saved", "already_saved": "Already saved", "not_found": "Not found", "unknown_action": "Unknown action", "please_restart": "Please restart", "yes": "Yes", "no": "No", "location": "Location", "website": "Website", "phone": "Phone", "email": "Email", "overview": "Overview", "fees_funding": "Fees & Funding", "admission": "Admission", "outcomes": "Career Outcomes", "contacts": "Contact Information", "degree_type": "Degree Type", "field_tag": "Field", "duration": "Duration", "credits": "Credits", "language_instruction": "Language of Instruction", "intake_months": "Intake Months", "tuition_bracket": "Tuition Bracket", "scholarship_available": "Scholarship Available", "scholarship_details": "Scholarship Details", "other_costs": "Other Costs", "requirements": "Requirements", "entrance_exam": "Entrance Exam", "min_gpa": "Minimum GPA", "application_deadline": "Application Deadline", "accreditation": "Accreditation", "median_salary": "Median Salary", "internship_required": "Internship Required", "global_ranking": "Global Ranking", "alumni_highlight": "Alumni Highlight", "welcome_eduguide": "Welcome to EduGuideBot", "home_description": "Your AI-powered university recommendation assistant for Cambodia", "choose_action": "Choose an action", "take_quiz": "15-Q Quiz", "surprise_me": "Surprise Me", "filter": "Filter", "browse_majors": "Browse Majors", "shortlist": "Shortlist", "help_button": "Help", "language_toggle": "EN / ខ្មែរ", "programme_not_found": "Programme not found", "error_loading_programme": "Error loading program", "surprise_recommendations": "Random Recommendations", "error_generating_recommendations": "Error generating recommendations", "browse_majors_coming_soon": "Major browsing coming soon", "use_quiz_instead": "Please use the 15-question quiz for now", "shortlist_empty": "Your shortlist is empty", "save_majors_hint": "Save majors from recommendations to see them here", "your_shortlist": "Your Shortlist", "help_quiz": "Take a comprehensive 15-question assessment for personalized recommendations", "help_surprise": "Get random recommendations to explore new possibilities", "help_filter": "Filter programs by budget, field, or location", "help_shortlist": "Save interesting programs to review later", "help_language": "Switch between English and Khmer languages", "quick_start": "Quick Start", "phnom_penh_only": "I'm in Phnom Penh", "low_tuition": "Low-Tuition Majors", "guide_me": "Guide Me (4 Q)", "trending": "Trending Majors", "share": "Share", "feedback": "Did you like these?", "like": "👍 Yes", "dislike": "👎 No", "thanks_feedback": "Thanks for the feedback!", "poll_followup": "Tell us what was wrong?", "no_trending": "No trending majors yet", "phnom_penh_focused": "Phnom Penh Programs", "low_tuition_focused": "Low-Cost Programs", "wizard_intro": "Quick 4-question assessment for instant recommendations", "wizard_error": "Wizard setup error", "invalid_share_link": "Invalid share link"}