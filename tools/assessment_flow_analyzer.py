#!/usr/bin/env python3
"""
EduGuideBot Assessment Flow and Button Function Analyzer
Comprehensive testing and debugging of the complete assessment flow and all interactive buttons
"""

import asyncio
import logging
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, List, Any

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.bot.handlers import (
    start_assessment, start_quiz_callback, handle_question_answer, 
    show_recommendations, home_screen_callback, quick_start_callback,
    surprise_me_callback, browse_majors_callback, shortlist_callback,
    help_callback, language_toggle_callback, restart_callback,
    back_callback, refresh_callback, detail_callback, compare_callback,
    filters_callback, save_callback, remove_callback, section_callback,
    trending_callback, feedback_callback
)
from src.bot.keyboards import (
    ASSESSMENT_QUESTIONS, ACTIVE_QUESTIONS, get_question_by_index,
    create_question_keyboard, create_progress_text
)
from src.bot.i18n import t

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AssessmentFlowAnalyzer:
    """Comprehensive analyzer for the assessment flow and button functions"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.test_results = {}
        
    def log_error(self, test_name: str, error: str):
        """Log an error during testing"""
        self.errors.append(f"{test_name}: {error}")
        logger.error(f"❌ {test_name}: {error}")
        
    def log_warning(self, test_name: str, warning: str):
        """Log a warning during testing"""
        self.warnings.append(f"{test_name}: {warning}")
        logger.warning(f"⚠️ {test_name}: {warning}")
        
    def log_success(self, test_name: str, message: str = ""):
        """Log a successful test"""
        self.test_results[test_name] = "PASS"
        logger.info(f"✅ {test_name}: {message}")

    async def test_assessment_questions_structure(self):
        """Test the structure and completeness of assessment questions"""
        test_name = "Assessment Questions Structure"
        
        try:
            # Check if we have the expected number of questions
            if len(ASSESSMENT_QUESTIONS) != 15:
                self.log_error(test_name, f"Expected 15 questions, found {len(ASSESSMENT_QUESTIONS)}")
                return

            if len(ACTIVE_QUESTIONS) != 15:
                self.log_error(test_name, f"Expected 15 active questions, found {len(ACTIVE_QUESTIONS)}")
                return
            
            # Check each question structure
            required_keys = ['id', 'question_key', 'options']
            for i, question in enumerate(ASSESSMENT_QUESTIONS):
                for key in required_keys:
                    if key not in question:
                        self.log_error(test_name, f"Question {i} missing required key: {key}")
                        return
                        
                # Check options structure
                if not isinstance(question['options'], list) or len(question['options']) == 0:
                    self.log_error(test_name, f"Question {i} has invalid options structure")
                    return
                    
                for option in question['options']:
                    if not isinstance(option, tuple) or len(option) != 2:
                        self.log_error(test_name, f"Question {i} has invalid option format: {option}")
                        return
            
            self.log_success(test_name, f"All {len(ASSESSMENT_QUESTIONS)} questions properly structured")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_question_keyboard_generation(self):
        """Test keyboard generation for all questions"""
        test_name = "Question Keyboard Generation"
        
        try:
            for i, question in enumerate(ACTIVE_QUESTIONS):
                for lang in ['kh', 'en']:
                    try:
                        keyboard = create_question_keyboard(question, lang)
                        
                        # Check keyboard structure
                        if not hasattr(keyboard, 'inline_keyboard'):
                            self.log_error(test_name, f"Question {i} keyboard missing inline_keyboard attribute")
                            return
                            
                        # Check number of buttons matches options
                        if len(keyboard.inline_keyboard) != len(question['options']):
                            self.log_error(test_name, f"Question {i} button count mismatch: {len(keyboard.inline_keyboard)} vs {len(question['options'])}")
                            return
                            
                        # Check callback data format
                        for j, row in enumerate(keyboard.inline_keyboard):
                            if len(row) != 1:
                                self.log_error(test_name, f"Question {i} row {j} should have exactly 1 button")
                                return
                                
                            button = row[0]
                            expected_callback = f"answer_{question['id']}_{question['options'][j][0]}"
                            if button.callback_data != expected_callback:
                                self.log_error(test_name, f"Question {i} button {j} callback mismatch: {button.callback_data} vs {expected_callback}")
                                return
                                
                    except Exception as e:
                        self.log_error(test_name, f"Question {i} ({lang}): {e}")
                        return
            
            self.log_success(test_name, f"All {len(ACTIVE_QUESTIONS)} questions generate valid keyboards")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_progress_tracking(self):
        """Test progress text generation"""
        test_name = "Progress Tracking"
        
        try:
            for i in range(1, 16):  # 1-based question numbers
                for lang in ['kh', 'en']:
                    try:
                        progress_text = create_progress_text(i, lang, 15)
                        
                        # Check that progress text is not empty
                        if not progress_text or len(progress_text.strip()) == 0:
                            self.log_error(test_name, f"Empty progress text for question {i} ({lang})")
                            return
                            
                        # Check that it contains progress bar elements
                        if '█' not in progress_text and '░' not in progress_text:
                            self.log_warning(test_name, f"Question {i} ({lang}) missing progress bar elements")
                            
                    except Exception as e:
                        self.log_error(test_name, f"Question {i} ({lang}): {e}")
                        return
            
            self.log_success(test_name, "Progress tracking works for all questions")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_assessment_flow_handlers(self):
        """Test the main assessment flow handlers"""
        test_name = "Assessment Flow Handlers"
        
        try:
            # Create mock objects
            mock_update = MagicMock()
            mock_context = MagicMock()
            mock_context.user_data = {}
            
            # Test start_quiz_callback
            mock_update.callback_query = AsyncMock()
            mock_update.message = None
            
            with patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock) as mock_edit:
                result = await start_quiz_callback(mock_update, mock_context)
                
                # Check that user data was initialized
                if 'answers' not in mock_context.user_data:
                    self.log_error(test_name, "start_quiz_callback didn't initialize answers")
                    return
                    
                if 'current_question' not in mock_context.user_data:
                    self.log_error(test_name, "start_quiz_callback didn't initialize current_question")
                    return
                    
                if mock_context.user_data['current_question'] != 0:
                    self.log_error(test_name, f"start_quiz_callback set wrong initial question: {mock_context.user_data['current_question']}")
                    return
            
            # Test handle_question_answer
            mock_context.user_data = {
                'answers': {},
                'current_question': 0
            }
            mock_update.callback_query.data = "answer_location_preference_pp"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock) as mock_answer, \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock) as mock_edit:
                
                result = await handle_question_answer(mock_update, mock_context)
                
                # Check that answer was stored
                if 'location_preference' not in mock_context.user_data['answers']:
                    self.log_error(test_name, "handle_question_answer didn't store answer")
                    return
                    
                if mock_context.user_data['answers']['location_preference'] != 'pp':
                    self.log_error(test_name, f"handle_question_answer stored wrong answer: {mock_context.user_data['answers']['location_preference']}")
                    return
                    
                # Check that question index was incremented
                if mock_context.user_data['current_question'] != 1:
                    self.log_error(test_name, f"handle_question_answer didn't increment question: {mock_context.user_data['current_question']}")
                    return
            
            self.log_success(test_name, "Assessment flow handlers work correctly")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_button_callback_patterns(self):
        """Test that all button callback patterns are properly registered"""
        test_name = "Button Callback Patterns"
        
        # Test patterns that should be handled
        test_patterns = [
            ("START_QUIZ", "start_quiz_callback"),
            ("HOME", "home_screen_callback"),
            ("QS_SURPRISE", "quick_start_callback"),
            ("QS_PP", "quick_start_callback"),
            ("BROWSE_MAJORS", "browse_majors_callback"),
            ("SHORTLIST_VIEW", "shortlist_callback"),
            ("HELP_INFO", "help_callback"),
            ("LANG_TOGGLE", "language_toggle_callback"),
            ("RESTART", "restart_callback"),
            ("BACK", "back_callback"),
            ("REFRESH", "refresh_callback"),
            ("FILTERS_HOME", "filters_callback"),
            ("DET_123", "detail_callback"),
            ("CMP_456", "compare_callback"),
            ("SAVE_789", "save_callback"),
            ("REMOVE_101", "remove_callback"),
            ("SEC_123_1", "section_callback"),
            ("TREND_456", "trending_callback"),
            ("FB_UP", "feedback_callback"),
            ("FB_DOWN", "feedback_callback"),
            ("answer_location_preference_pp", "handle_question_answer"),
        ]
        
        try:
            from src.bot.telegram_safe import validate_callback_pattern
            
            unhandled_patterns = []
            for pattern, expected_handler in test_patterns:
                if not validate_callback_pattern(pattern):
                    unhandled_patterns.append(f"{pattern} -> {expected_handler}")
            
            if unhandled_patterns:
                self.log_error(test_name, f"Unhandled patterns: {unhandled_patterns}")
                return
            
            self.log_success(test_name, f"All {len(test_patterns)} callback patterns are properly handled")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_home_screen_buttons(self):
        """Test home screen button functionality"""
        test_name = "Home Screen Buttons"

        try:
            mock_update = MagicMock()
            mock_context = MagicMock()
            mock_context.user_data = {}
            mock_update.callback_query = AsyncMock()

            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock) as mock_answer, \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock) as mock_edit, \
                 patch('src.bot.ui.create_home_screen') as mock_home:

                mock_home.return_value = ("Home Screen", MagicMock())

                # Test home screen callback
                await home_screen_callback(mock_update, mock_context)
                mock_answer.assert_called_once()
                mock_edit.assert_called_once()

            self.log_success(test_name, "Home screen buttons work correctly")

        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_recommendation_generation(self):
        """Test recommendation generation flow"""
        test_name = "Recommendation Generation"

        try:
            mock_update = MagicMock()
            mock_context = MagicMock()
            mock_context.user_data = {
                'answers': {
                    'location_preference': 'pp',
                    'budget_range': 'low',
                    'field_of_interest': 'stem',
                    'career_goal': 'tech',
                    'academic_strength': 'math'
                }
            }
            mock_update.callback_query = AsyncMock()

            with patch('src.core.hybrid_recommender.get_recommendations') as mock_rec, \
                 patch('src.bot.ui.create_recommendations_view') as mock_view, \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock) as mock_edit, \
                 patch('src.bot.utils.cache_recommendations') as mock_cache_rec, \
                 patch('src.bot.utils.cache_user_answers') as mock_cache_ans, \
                 patch('src.bot.utils.log_recommendation_metrics') as mock_log:

                # Mock successful recommendation generation
                mock_rec.return_value = [
                    {'major_id': '1', 'major_name_en': 'Computer Science', 'hybrid_score': 0.95},
                    {'major_id': '2', 'major_name_en': 'Software Engineering', 'hybrid_score': 0.90}
                ]
                mock_view.return_value = ("Recommendations", MagicMock())

                result = await show_recommendations(mock_update, mock_context)

                # Check that recommendations were generated
                mock_rec.assert_called_once()
                mock_view.assert_called_once()
                mock_edit.assert_called_once()

            self.log_success(test_name, "Recommendation generation works correctly")

        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def run_comprehensive_analysis(self):
        """Run all tests and generate comprehensive report"""
        print("🚀 Starting EduGuideBot Assessment Flow and Button Analysis")
        print("=" * 70)
        
        # Run all tests
        await self.test_assessment_questions_structure()
        await self.test_question_keyboard_generation()
        await self.test_progress_tracking()
        await self.test_assessment_flow_handlers()
        await self.test_button_callback_patterns()
        await self.test_home_screen_buttons()
        await self.test_recommendation_generation()
        
        # Generate report
        print("\n" + "=" * 70)
        print("📊 COMPREHENSIVE ANALYSIS REPORT")
        print("=" * 70)
        
        total_tests = len(self.test_results) + len(self.errors)
        passed_tests = len(self.test_results)
        
        print(f"✅ Tests Passed: {passed_tests}")
        print(f"❌ Tests Failed: {len(self.errors)}")
        print(f"⚠️ Warnings: {len(self.warnings)}")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "📈 Success Rate: 0%")
        
        if self.errors:
            print("\n🔥 CRITICAL ISSUES FOUND:")
            for error in self.errors:
                print(f"   ❌ {error}")
        
        if self.warnings:
            print("\n⚠️ WARNINGS:")
            for warning in self.warnings:
                print(f"   ⚠️ {warning}")
        
        if not self.errors:
            print("\n🎉 ALL CRITICAL TESTS PASSED!")
            print("✅ Assessment flow is functional")
            print("✅ All button callbacks are properly registered")
            print("✅ Question structure is valid")
            print("✅ Recommendation generation works")
        else:
            print("\n🚨 ISSUES REQUIRE IMMEDIATE ATTENTION")
            print("❌ Assessment flow has critical problems")
            print("🔧 Fix required before deployment")
        
        return len(self.errors) == 0


async def main():
    """Main entry point"""
    analyzer = AssessmentFlowAnalyzer()
    success = await analyzer.run_comprehensive_analysis()
    
    if success:
        print("\n🚀 Ready for Phase 2: Complete button function testing")
        return 0
    else:
        print("\n🛑 Phase 1 issues must be resolved first")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
