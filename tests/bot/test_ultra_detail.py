"""
Tests for ultra-detail view system
"""
import pytest
from unittest.mock import Mock, AsyncMock
from telegram import Update, CallbackQuery, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from src.bot.handlers import section_callback
from src.bot.ui import render_section, _render_overview_section, _render_fees_section


@pytest.fixture
def mock_update():
    """Create mock update with callback query"""
    update = Mock(spec=Update)
    update.callback_query = Mock(spec=CallbackQuery)
    update.callback_query.answer = AsyncMock()
    update.callback_query.edit_message_text = AsyncMock()
    return update


@pytest.fixture
def mock_context():
    """Create mock context with user data"""
    context = Mock(spec=ContextTypes.DEFAULT_TYPE)
    context.user_data = {
        'language': 'en',
        'last_recs': {
            'computer-science': {
                'major_id': 'computer-science',
                'major_name_en': 'Computer Science',
                'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
                'university_name_en': 'Royal University of Phnom Penh',
                'university_name_kh': 'សាកលវិទ្យាល័យភូមិន្ទ',
                'degree_type': 'Bachelor',
                'field_tag': 'STEM',
                'programme_duration': '4 years',
                'credit_count': '120',
                'language_of_instruction': 'English',
                'intake_months': 'September, January',
                'tuition_fees_usd': '300',
                'tuition_bracket': 'Low',
                'scholarship_availability': True,
                'scholarship_details': 'Merit-based scholarships available',
                'other_costs_note': 'Books and materials: $100/year',
                'admission_req': 'High school diploma with good grades',
                'entrance_exam': 'Required',
                'min_gpa': '3.0',
                'application_deadline': 'August 15',
                'accreditation_body': 'Ministry of Education',
                'employment_rate': '92',
                'median_salary_usd': '800',
                'internship_required': True,
                'global_ranking': 'Top 10 in Cambodia',
                'alumni_highlight': 'Many work at top tech companies',
                'contact_phone': '+855-12-345-678',
                'contact_email': '<EMAIL>',
                'facebook_url': 'https://facebook.com/rupp',
                'telegram_url': 'https://t.me/rupp',
                'website_url': 'https://rupp.edu.kh',
                'map_url': 'https://maps.google.com/rupp'
            }
        }
    }
    return context


@pytest.fixture
def sample_programme():
    """Sample programme data for testing"""
    return {
        'major_id': 'computer-science',
        'major_name_en': 'Computer Science',
        'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
        'university_name_en': 'Royal University of Phnom Penh',
        'university_name_kh': 'សាកលវិទ្យាល័យភូមិន្ទ',
        'degree_type': 'Bachelor',
        'field_tag': 'STEM',
        'programme_duration': '4 years',
        'credit_count': '120',
        'tuition_fees_usd': '300',
        'employment_rate': '92'
    }


class TestSectionNavigation:
    """Test section navigation functionality"""
    
    @pytest.mark.asyncio
    async def test_section_navigation_first_disables_prev(self, mock_update, mock_context):
        """Test that first section disables previous button"""
        mock_update.callback_query.data = "SEC_computer-science_0"
        
        await section_callback(mock_update, mock_context)
        
        # Check that edit_message_text was called
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Get the call arguments
        call_args = mock_update.callback_query.edit_message_text.call_args
        reply_markup = call_args.kwargs['reply_markup']
        
        # Check that there's no previous button (only next and back)
        buttons = []
        for row in reply_markup.inline_keyboard:
            for button in row:
                buttons.append(button.text)
        
        # Check for both English and Khmer text
        prev_buttons = [b for b in buttons if "Prev" in b or "មុន" in b]
        next_buttons = [b for b in buttons if "Next" in b or "បន្ទាប់" in b]
        back_buttons = [b for b in buttons if "Back" in b or "ត្រលប់" in b]

        assert len(prev_buttons) == 0  # No previous button on first section
        assert len(next_buttons) > 0 or len(back_buttons) > 0  # Should have next or back button
    
    @pytest.mark.asyncio
    async def test_section_navigation_last_disables_next(self, mock_update, mock_context):
        """Test that last section disables next button"""
        mock_update.callback_query.data = "SEC_computer-science_4"  # Last section (contacts)
        
        await section_callback(mock_update, mock_context)
        
        # Check that edit_message_text was called
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Get the call arguments
        call_args = mock_update.callback_query.edit_message_text.call_args
        reply_markup = call_args.kwargs['reply_markup']
        
        # Check that there's no next button
        buttons = []
        for row in reply_markup.inline_keyboard:
            for button in row:
                buttons.append(button.text)
        
        # Check for both English and Khmer text
        prev_buttons = [b for b in buttons if "Prev" in b or "មុន" in b]
        next_buttons = [b for b in buttons if "Next" in b or "បន្ទាប់" in b]
        back_buttons = [b for b in buttons if "Back" in b or "ត្រលប់" in b]

        assert len(next_buttons) == 0  # No next button on last section
        assert len(prev_buttons) > 0 or len(back_buttons) > 0  # Should have prev or back button
    
    @pytest.mark.asyncio
    async def test_invalid_section_index_defaults_to_overview(self, mock_update, mock_context):
        """Test that invalid section index defaults to overview"""
        mock_update.callback_query.data = "SEC_computer-science_99"  # Invalid section
        
        await section_callback(mock_update, mock_context)
        
        # Check that edit_message_text was called
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Get the call arguments
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        
        # Should show overview section (section 0)
        assert "Overview" in message_text or "ទិដ្ឋភាពទូទៅ" in message_text
    
    @pytest.mark.asyncio
    async def test_missing_programme_shows_error(self, mock_update, mock_context):
        """Test that missing programme shows error message"""
        mock_update.callback_query.data = "SEC_nonexistent-major_0"
        
        await section_callback(mock_update, mock_context)
        
        # Check that edit_message_text was called with error message
        mock_update.callback_query.edit_message_text.assert_called_once()
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.args[0]
        
        assert "❌" in message_text
        assert "not found" in message_text.lower() or "រកមិនឃើញ" in message_text


class TestSectionRendering:
    """Test section rendering functions"""
    
    def test_render_overview_section(self, sample_programme):
        """Test overview section rendering"""
        message_text, reply_markup = render_section(sample_programme, 0, 'en')
        
        assert "Overview" in message_text
        assert "Computer Science" in message_text
        assert "Royal University of Phnom Penh" in message_text
        assert "Bachelor" in message_text
        assert "STEM" in message_text
        assert "4 years" in message_text
        
        # Check navigation buttons
        assert isinstance(reply_markup, InlineKeyboardMarkup)
        assert len(reply_markup.inline_keyboard) >= 1
    
    def test_render_fees_section(self, sample_programme):
        """Test fees section rendering"""
        message_text, reply_markup = render_section(sample_programme, 1, 'en')
        
        assert "Fees & Funding" in message_text
        assert "300 USD" in message_text
        
        # Check navigation buttons (should have both prev and next)
        buttons = []
        for row in reply_markup.inline_keyboard:
            for button in row:
                buttons.append(button.text)
        
        assert "◀ Prev" in buttons
        assert "Next ▶" in buttons
    
    def test_contacts_section_contains_urls(self, sample_programme):
        """Test that contacts section contains contact information"""
        # Add contact info to sample programme
        sample_programme.update({
            'contact_phone': '+855-12-345-678',
            'contact_email': '<EMAIL>',
            'website_url': 'https://rupp.edu.kh'
        })
        
        message_text, reply_markup = render_section(sample_programme, 4, 'en')
        
        assert "Contact Information" in message_text
        assert "+855-12-345-678" in message_text
        assert "<EMAIL>" in message_text
        assert "https://rupp.edu.kh" in message_text
    
    def test_section_rendering_khmer(self, sample_programme):
        """Test section rendering in Khmer"""
        message_text, reply_markup = render_section(sample_programme, 0, 'kh')
        
        assert "ទិដ្ឋភាពទូទៅ" in message_text
        assert "វិទ្យាសាស្ត្រកុំព្យូទ័រ" in message_text
        assert "សាកលវិទ្យាល័យភូមិន្ទ" in message_text


class TestCallbackDataValidation:
    """Test callback data parsing and validation"""
    
    @pytest.mark.asyncio
    async def test_invalid_callback_format(self, mock_update, mock_context):
        """Test handling of invalid callback format"""
        mock_update.callback_query.data = "INVALID_FORMAT"
        
        await section_callback(mock_update, mock_context)
        
        # Should not call edit_message_text for invalid format
        mock_update.callback_query.edit_message_text.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_non_numeric_section(self, mock_update, mock_context):
        """Test handling of non-numeric section index"""
        mock_update.callback_query.data = "SEC_computer-science_invalid"
        
        await section_callback(mock_update, mock_context)
        
        # Should not call edit_message_text for invalid section
        mock_update.callback_query.edit_message_text.assert_not_called()
    
    def test_callback_data_length_compliance(self, sample_programme):
        """Test that callback data stays under 64 bytes"""
        major_id = sample_programme['major_id']
        
        for section_index in range(5):
            callback_data = f"SEC_{major_id}_{section_index}"
            assert len(callback_data.encode('utf-8')) <= 64, f"Callback data too long: {callback_data}"
